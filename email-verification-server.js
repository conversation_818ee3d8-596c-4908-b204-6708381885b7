/**
 * Node.js 邮箱验证码接收服务器
 * 基于真实的IMAP协议接收验证码邮件
 * 
 * 安装依赖：
 * npm install imap mailparser express cors
 * 
 * 使用方法：
 * node email-verification-server.js
 */

const Imap = require('imap');
const { simpleParser } = require('mailparser');
const express = require('express');
const cors = require('cors');

class EmailVerificationServer {
    constructor() {
        // IMAP配置
        this.imapConfig = {
            user: '<EMAIL>',
            password: 'okwbkuwqovuschdg',
            host: 'imap.qq.com',
            port: 993,
            tls: true,
            tlsOptions: {
                rejectUnauthorized: false
            }
        };
        
        this.verificationCodes = new Map();
        this.imap = null;
        this.isConnected = false;
        
        // 创建Express服务器
        this.app = express();
        this.app.use(cors());
        this.app.use(express.json());
        
        this.setupRoutes();
    }
    
    // 设置API路由
    setupRoutes() {
        // 获取最新验证码
        this.app.get('/api/latest-code', (req, res) => {
            const serviceName = req.query.service;
            const code = this.getLatestVerificationCode(serviceName);
            res.json({ code, timestamp: new Date().toISOString() });
        });
        
        // 获取所有验证码
        this.app.get('/api/all-codes', (req, res) => {
            const codes = this.getAllVerificationCodes();
            res.json({ codes, count: codes.length });
        });
        
        // 连接邮箱
        this.app.post('/api/connect', async (req, res) => {
            try {
                await this.connect();
                res.json({ success: true, message: '连接成功' });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // 获取验证码邮件
        this.app.post('/api/fetch', async (req, res) => {
            try {
                const emails = await this.fetchVerificationEmails();
                res.json({ success: true, emails, count: emails.length });
            } catch (error) {
                res.status(500).json({ success: false, error: error.message });
            }
        });
        
        // 连接状态
        this.app.get('/api/status', (req, res) => {
            res.json({ 
                connected: this.isConnected,
                codesCount: this.verificationCodes.size,
                email: this.imapConfig.user
            });
        });
        
        // 静态文件服务
        this.app.use(express.static('.'));
    }
    
    // 连接到IMAP服务器
    connect() {
        return new Promise((resolve, reject) => {
            console.log('正在连接到IMAP服务器...');
            
            this.imap = new Imap(this.imapConfig);
            
            this.imap.once('ready', () => {
                console.log('IMAP连接成功');
                this.isConnected = true;
                resolve();
            });
            
            this.imap.once('error', (err) => {
                console.error('IMAP连接错误:', err);
                this.isConnected = false;
                reject(err);
            });
            
            this.imap.once('end', () => {
                console.log('IMAP连接已断开');
                this.isConnected = false;
            });
            
            this.imap.connect();
        });
    }
    
    // 获取验证码邮件
    fetchVerificationEmails() {
        return new Promise((resolve, reject) => {
            if (!this.isConnected) {
                reject(new Error('未连接到邮箱服务器'));
                return;
            }
            
            this.imap.openBox('INBOX', false, (err, box) => {
                if (err) {
                    reject(err);
                    return;
                }
                
                console.log('正在搜索验证码邮件...');
                
                // 搜索最近的邮件
                const searchCriteria = [
                    'UNSEEN', // 未读邮件
                    ['SINCE', new Date(Date.now() - 24 * 60 * 60 * 1000)] // 最近24小时
                ];
                
                this.imap.search(searchCriteria, (err, results) => {
                    if (err) {
                        reject(err);
                        return;
                    }
                    
                    if (!results || results.length === 0) {
                        console.log('没有找到新的验证码邮件');
                        resolve([]);
                        return;
                    }
                    
                    console.log(`找到 ${results.length} 封新邮件`);
                    
                    const fetch = this.imap.fetch(results, {
                        bodies: '',
                        markSeen: false
                    });
                    
                    const emails = [];
                    
                    fetch.on('message', (msg, seqno) => {
                        let buffer = '';
                        
                        msg.on('body', (stream, info) => {
                            stream.on('data', (chunk) => {
                                buffer += chunk.toString('utf8');
                            });
                        });
                        
                        msg.once('end', async () => {
                            try {
                                const parsed = await simpleParser(buffer);
                                const email = this.processEmail(parsed);
                                if (email) {
                                    emails.push(email);
                                    
                                    // 添加到验证码缓存
                                    const key = `${email.service}_${email.timestamp}`;
                                    this.verificationCodes.set(key, email);
                                }
                            } catch (error) {
                                console.error('解析邮件失败:', error);
                            }
                        });
                    });
                    
                    fetch.once('error', (err) => {
                        reject(err);
                    });
                    
                    fetch.once('end', () => {
                        console.log(`处理完成，获取到 ${emails.length} 封验证码邮件`);
                        resolve(emails);
                    });
                });
            });
        });
    }
    
    // 处理邮件，提取验证码
    processEmail(parsed) {
        const subject = parsed.subject || '';
        const from = parsed.from?.text || '';
        const text = parsed.text || '';
        const html = parsed.html || '';

        console.log('=== 处理邮件 ===');
        console.log('发件人:', from);
        console.log('主题:', subject);
        console.log('文本内容:', text.substring(0, 200) + '...');
        console.log('HTML内容:', html.substring(0, 200) + '...');

        // 检查是否是验证码邮件
        const verificationKeywords = ['验证码', 'verification', 'code', '验证', 'verify', 'otp', '动态密码', '安全码'];
        const isVerificationEmail = verificationKeywords.some(keyword =>
            subject.toLowerCase().includes(keyword) ||
            text.toLowerCase().includes(keyword) ||
            html.toLowerCase().includes(keyword)
        );

        console.log('是否为验证码邮件:', isVerificationEmail);

        if (!isVerificationEmail) {
            console.log('不是验证码邮件，跳过');
            return null;
        }

        // 提取验证码
        const content = text + ' ' + html;
        const code = this.extractVerificationCode(content);
        console.log('提取到的验证码:', code);

        if (!code) {
            console.log('未能提取到验证码');
            return null;
        }

        // 提取服务名称
        const service = this.extractServiceName(from, subject);

        const result = {
            from,
            subject,
            body: text,
            code,
            timestamp: new Date().toISOString(),
            service
        };

        console.log('处理结果:', result);
        console.log('=== 处理完成 ===');

        return result;
    }
    
    // 提取验证码
    extractVerificationCode(content) {
        const patterns = [
            /验证码[：:\s]*(\d{4,8})/i,
            /验证码是[：:\s]*(\d{4,8})/i,
            /code[：:\s]*(\d{4,8})/i,
            /verification code[：:\s]*(\d{4,8})/i,
            /(\d{6})/g,  // 6位数字
            /(\d{4})/g   // 4位数字
        ];
        
        for (const pattern of patterns) {
            const matches = content.match(pattern);
            if (matches) {
                // 对于全局匹配，选择最可能的验证码
                if (pattern.global) {
                    // 优先选择6位数字
                    const sixDigit = matches.find(match => match.length === 6);
                    if (sixDigit) return sixDigit;
                    
                    // 其次选择4位数字
                    const fourDigit = matches.find(match => match.length === 4);
                    if (fourDigit) return fourDigit;
                    
                    return matches[0];
                } else {
                    return matches[1];
                }
            }
        }
        
        return null;
    }
    
    // 提取服务名称
    extractServiceName(from, subject) {
        // 从发件人地址提取域名
        const emailMatch = from.match(/@([^>\s]+)/);
        if (emailMatch) {
            return emailMatch[1];
        }
        
        // 从主题提取
        const subjectWords = subject.split(/[\s\-_]+/);
        return subjectWords[0] || 'unknown';
    }
    
    // 获取最新验证码
    getLatestVerificationCode(serviceName = null) {
        const codes = Array.from(this.verificationCodes.values());
        
        // 按时间排序
        codes.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        if (serviceName) {
            const serviceCode = codes.find(code => 
                code.service.toLowerCase().includes(serviceName.toLowerCase()) ||
                code.from.toLowerCase().includes(serviceName.toLowerCase())
            );
            return serviceCode ? serviceCode.code : null;
        }
        
        return codes.length > 0 ? codes[0].code : null;
    }
    
    // 获取所有验证码
    getAllVerificationCodes() {
        const codes = Array.from(this.verificationCodes.values());
        return codes.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
    }
    
    // 清除过期验证码
    clearExpiredCodes() {
        const now = new Date();
        const expiredKeys = [];
        
        for (const [key, code] of this.verificationCodes) {
            const codeTime = new Date(code.timestamp);
            const diffMinutes = (now - codeTime) / (1000 * 60);
            
            if (diffMinutes > 30) {
                expiredKeys.push(key);
            }
        }
        
        expiredKeys.forEach(key => this.verificationCodes.delete(key));
        
        if (expiredKeys.length > 0) {
            console.log(`已清除 ${expiredKeys.length} 个过期验证码`);
        }
    }
    
    // 启动服务器
    start(port = 3000) {
        // 定期清理过期验证码
        setInterval(() => {
            this.clearExpiredCodes();
        }, 5 * 60 * 1000); // 每5分钟清理一次
        
        this.app.listen(port, () => {
            console.log(`邮箱验证码接收服务器已启动: http://localhost:${port}`);
            console.log(`邮箱配置: ${this.imapConfig.user}`);
            console.log('API端点:');
            console.log('  GET  /api/latest-code - 获取最新验证码');
            console.log('  GET  /api/all-codes - 获取所有验证码');
            console.log('  POST /api/connect - 连接邮箱');
            console.log('  POST /api/fetch - 获取验证码邮件');
            console.log('  GET  /api/status - 连接状态');
        });
    }
}

// 如果直接运行此文件，启动服务器
if (require.main === module) {
    const server = new EmailVerificationServer();
    server.start();
}

module.exports = EmailVerificationServer;
